# Tindahan Store Database Organization

## 🎯 Master Schema File

**Use this file for all new deployments:**
- **`tindahan_unified_schema.sql`** - Complete, production-ready database schema

This unified schema consolidates all database functionality into a single, well-organized file that replaces all other database files.

## 📋 File Status Overview

### ✅ ACTIVE (Use This)
- **`tindahan_unified_schema.sql`** - **MASTER SCHEMA** - Use for all deployments
- **`README.md`** - This documentation file

### 🧹 RECENTLY UPDATED (2025-07-29)
- **Database folder professionally cleaned** - All redundant files removed
- **Customer profile fields verified** - Already integrated in unified schema
- **Debt management connectivity confirmed** - All features properly connected
- **🔒 PRODUCTION-SAFE VERSION** - Schema now preserves existing data when re-run
- **🛡️ DATA PROTECTION** - Your debt records and customer data are never lost
- **🔧 DATE TYPE FIX** - Fixed date casting errors for proper Supabase execution

### 🗑️ REMOVED (Redundant Files Cleaned Up)
All legacy and redundant database files have been professionally removed to maintain clean database organization:
- ~~`schema.sql`~~ - Consolidated into unified schema
- ~~`tindahan_complete_schema.sql`~~ - Replaced by unified schema
- ~~`debt_management_schema.sql`~~ - Integrated into unified schema
- ~~`migration_customer_profiles.sql`~~ - Applied to unified schema
- ~~`add_cloudinary_support.sql`~~ - Included in unified schema
- ~~`add_customer_profile_fields.sql`~~ - **REMOVED 2025-07-28** - Fields already in unified schema
- ~~`migrations/` folder~~ - **CLEANED UP 2025-07-28** - All migrations integrated
- ~~`fix_*.sql` files~~ - Fixes applied to unified schema

## 🚀 Quick Start

1. **For New Deployments:**
   ```sql
   -- Copy and paste the entire contents of:
   database/tindahan_unified_schema.sql
   ```

2. **Run in Supabase SQL Editor**
3. **Verify automatic setup completion**

### 🔒 **PRODUCTION-SAFE RE-RUNS**
- **Safe to re-run multiple times** - Your existing data is preserved
- **Sample data protection** - Only added to empty tables
- **No data loss** - Your debt records and customer data are never overwritten
- **Perfect for schema updates** - Add new features without losing data

## 🎯 Unified Schema Features

### 📊 Complete Database Structure
- **Products Table**: 25+ sample products across 8 categories
- **Customers Table**: 8 diverse customer profiles with Cloudinary support
- **Customer Debts Table**: 15+ realistic debt transactions
- **Customer Payments Table**: 12+ payment records with family member tracking
- **Customer Balances View**: Enhanced with status indicators and payment percentages

### ⚡ Performance Optimizations
- **20+ Specialized Indexes**: Including fuzzy search support
- **Composite Indexes**: For complex queries
- **Partial Indexes**: For filtered operations
- **GIN Indexes**: For text search capabilities

### 🛡️ Security Features
- **Row Level Security**: Optimized for custom authentication
- **Data Validation**: Comprehensive constraints and business rules
- **Secure Functions**: SECURITY DEFINER with explicit search_path
- **Input Sanitization**: Multiple validation layers

### 🔧 Automation Features
- **Automatic Timestamps**: Triggers for created_at/updated_at
- **Payment Validation**: Business rule enforcement
- **Calculated Fields**: Automatic total_amount computation
- **Status Tracking**: Balance status and payment percentage calculation

### 📝 Sample Data
- **Realistic Test Data**: Covers all functionality
- **Diverse Categories**: Products across 8 store categories
- **Customer Profiles**: Various customer types and preferences
- **Transaction History**: Debts and payments with family member tracking
- **Payment Methods**: Cash, GCash, PayMaya, Bank Transfer, etc.

## 🔧 Database Synchronization Troubleshooting

### Issue: Records appear in localhost but not in Supabase SQL Editor

If you're experiencing synchronization issues (like Dave Mejos appearing in localhost but not in Supabase):

1. **Verify Database Connection**:
   ```sql
   -- Run this in Supabase SQL Editor:
   SELECT customer_name, customer_family_name, created_at
   FROM customer_debts
   ORDER BY created_at DESC LIMIT 10;
   ```

2. **Check Environment Variables**:
   - Verify `.env.local` has correct `NEXT_PUBLIC_SUPABASE_URL`
   - Confirm `NEXT_PUBLIC_SUPABASE_ANON_KEY` is valid
   - Ensure no typos in the credentials

3. **Test API Endpoints**:
   - Open browser Network tab
   - Add a new debt record
   - Check for failed API calls to `/api/debts`

4. **Use Integrated Verification**:
   ```bash
   # The unified schema now includes comprehensive diagnostic queries
   # Simply run the entire tindahan_unified_schema.sql file
   # All verification and troubleshooting tools are included
   ```

### Quick Diagnostic Steps:
1. **Browser Console**: Check for JavaScript errors
2. **Network Tab**: Look for 400/500 HTTP errors
3. **Supabase Logs**: Check your project's logs in Supabase dashboard
4. **API Test**: Use `scripts/test-debt-api.js` to test endpoints

## 🔄 Migration from Legacy Files

If you're currently using any of the legacy files, simply:

1. **Backup your current data** (if in production)
2. **Run the unified schema** - it includes safe cleanup
3. **Verify the setup** using the built-in verification queries
4. **Test your application** - all API endpoints remain compatible

## 📈 Benefits of Unified Schema

### ✅ Simplified Management
- **Single File**: No more juggling multiple schema files
- **Conflict-Free**: Automatic cleanup prevents deployment issues
- **Version Control**: Easier to track changes and updates

### ✅ Enhanced Features
- **Advanced Search**: Fuzzy text search capabilities
- **Better Performance**: Optimized indexing strategy
- **Improved Security**: Enhanced RLS policies and validation
- **Status Tracking**: Real-time balance status and payment percentages

### ✅ Production Ready
- **Error Handling**: Safe to run multiple times
- **Data Integrity**: Comprehensive constraints and validation
- **Performance Optimized**: Specialized indexes for all query patterns
- **Future Proof**: Extensible design for additional features
- **🔒 Data Protection**: Preserves existing data when re-run
- **🛡️ Production-Safe**: Sample data only added to empty tables

## � Data Preservation & Safety

### 🛡️ **PRODUCTION-SAFE FEATURES**
The unified schema is designed to **NEVER LOSE YOUR DATA**:

1. **Safe Re-runs**: You can run the schema multiple times without data loss
2. **Smart Sample Data**: Only adds sample data if tables are completely empty
3. **Existing Data Protection**: Your debt records and customer data are preserved
4. **No Duplicates**: Sample data will never be duplicated

### 📋 **How It Works**
```sql
-- Sample data uses conditional insertion:
INSERT INTO customer_debts (...)
SELECT * FROM (VALUES (...)) AS sample_data
WHERE NOT EXISTS (SELECT 1 FROM customer_debts LIMIT 1);
```

### ✅ **Safe Operations**
- ✅ Re-run schema for updates
- ✅ Add new features to existing database
- ✅ Fix schema issues without data loss
- ✅ Update constraints and indexes

### ⚠️ **Best Practices**
1. **Always backup before major changes** (though schema is safe)
2. **Test in development first** before production updates
3. **Monitor the completion messages** for verification
4. **Keep this README updated** with any customizations

## �🛠️ Maintenance

### Regular Tasks
- **Monitor Performance**: Use built-in indexes for optimal queries
- **Add Real Data**: Your debt records are preserved when adding new data
- **Update Documentation**: Keep API documentation in sync

### Schema Updates
- **Use the unified schema**: Always update the master file
- **Safe to re-run**: Your data is protected during updates
- **Test Changes**: Verify compatibility with existing application
- **Document Changes**: Update this README when making modifications

## 📞 Support

For questions about the database schema:
1. **Check the unified schema comments**: Comprehensive documentation included
2. **Review verification queries**: Built-in validation and testing
3. **Test with sample data**: Realistic data for all scenarios

---

**🎯 Remember**: Always use `tindahan_unified_schema.sql` for new deployments. This master schema provides everything you need in a single, professional, production-ready file.
